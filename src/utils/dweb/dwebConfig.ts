import { invoke } from '@tauri-apps/api/core';

export interface DwebConfig {
  auto_detect: boolean;
  port: number;
  timeout_seconds: number;
}

// Get current dweb configuration
export async function getDwebConfig(): Promise<DwebConfig> {
  try {
    return await invoke('get_dweb_config');
  } catch (error) {
    console.error('Failed to get dweb config:', error);
    // Return default config on error
    return {
      auto_detect: true,
      port: 8080,
      timeout_seconds: 2
    };
  }
}

// Set dweb configuration
export async function setDwebConfig(config: DwebConfig): Promise<string> {
  try {
    return await invoke('set_dweb_config', { config });
  } catch (error) {
    console.error('Failed to set dweb config:', error);
    throw error;
  }
}
